:root {
    --primary-color: #d7263d;
    --secondary-color: #ffb400;
    --background-color: #f8f9fa;
    --card-background: #ffffff;
    --text-color: #222;
    --shadow: 0 4px 8px rgba(0,0,0,0.08);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

header {
    background: var(--primary-color);
    color: #fff;
    text-align: center;
    padding: 2rem 1rem;
}

header h1 {
    margin: 0;
    font-size: 2.5rem;
    color: #fff;
}

header p {
    margin: 0.5rem 0 0;
    opacity: 0.9;
}

main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

.controls {
    display: flex;
    justify-content: center;
    margin-bottom: 1em;
}

.controls input[type="text"] {
    width: 320px;
    padding: 0.5em 1em;
    font-size: 1em;
    border: 1px solid #bdbdbd;
    border-radius: 6px;
    outline: none;
    transition: border 0.2s;
}

.controls input[type="text"]:focus {
    border: 1.5px solid #3949ab;
}

.event-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
}

.event-card {
    background: var(--card-background);
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 1.5rem;
    transition: transform 0.2s, box-shadow 0.2s;
    border-left: 5px solid var(--secondary-color);
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.event-card h2 {
    margin-top: 0;
    color: var(--primary-color);
}

.event-card p {
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.special-note {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    padding: 0.5rem;
    border-radius: 4px;
    color: #d46b08;
    font-weight: bold;
}

.rain-date {
    color: #555;
    font-style: italic;
}

footer {
    text-align: center;
    padding: 2rem;
    margin-top: 2rem;
    color: #777;
    font-size: 0.9rem;
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.8rem;
    }
    .event-grid {
        grid-template-columns: 1fr;
    }
}

.boston-time {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 1em;
  color: #b85c38;
  text-align: center;
}

.event-layout {
  display: flex;
  gap: 2em;
}

.date-list {
  min-width: 140px;
  border-right: 1px solid #eee;
  padding-right: 1em;
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}

.date-list button {
  background: #f5f5f5;
  border: 1px solid #bdbdbd;
  border-radius: 6px;
  padding: 0.5em 1em;
  cursor: pointer;
  font-size: 1em;
  transition: background 0.2s, color 0.2s;
  color: var(--primary-color);
}
.date-list button.active {
  background: var(--secondary-color);
  color: #fff;
  font-weight: bold;
}

.event-grid {
  flex: 1;
}

h1 {
  font-size: 2em;
  margin-bottom: 0.3em;
  color: #b85c38;
}
h2 {
  font-size: 1.3em;
  margin: 1.2em 0 0.5em 0;
  color: var(--secondary-color);
}
h3 {
  font-size: 1.1em;
  margin: 0.8em 0 0.3em 0;
  color: #0a2463;
}