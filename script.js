document.addEventListener('DOMContentLoaded', () => {
    const eventListContainer = document.getElementById('event-list');
    const dateListContainer = document.getElementById('date-list');
    const searchInput = document.getElementById('searchInput');
    let allEvents = [];
    let eventsByDate = {};
    let currentSelectedDate = null;

    // 1. 显示波士顿时间
    function updateBostonTime() {
        const bostonTimeElem = document.getElementById('boston-time');
        const now = new Date();
        // 转为美东时间（波士顿）
        const options = { timeZone: 'America/New_York', year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' };
        const bostonTime = now.toLocaleString('en-US', options);
        bostonTimeElem.textContent = `Boston Time: ${bostonTime}`;
    }
    setInterval(updateBostonTime, 1000);
    updateBostonTime();

    // 监听搜索输入
    searchInput.addEventListener('input', () => {
        renderEventsForCurrentDate();
    });

    // 2. Fetch data from the JSON file
    fetch('data/fireworks_events_ma_2025.json')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            allEvents = data;
            groupEventsByDate();
            renderDateList();
            // Generate structured data for all events
            generateStructuredData();
            // Handle URL hash navigation first, before default date selection
            const hash = window.location.hash.substring(1);
            if (hash) {
                // If there's a hash, handle it and let it select the appropriate date
                handleHashNavigation();
            } else {
                // Only do default date selection if there's no hash
                const todayStr = getTodayDateStr();
                if (eventsByDate[todayStr]) {
                    selectDate(todayStr);
                } else {
                    const firstDate = Object.keys(eventsByDate)[0];
                    selectDate(firstDate);
                }
            }
        })
        .catch(error => {
            console.error("Could not fetch fireworks data:", error);
            eventListContainer.innerHTML = "<p>Sorry, we couldn't load the event data. Please try again later.</p>";
        });

    // 3. 按日期分组
    function groupEventsByDate() {
        eventsByDate = {};
        allEvents.forEach(event => {
            if (!eventsByDate[event.date]) {
                eventsByDate[event.date] = [];
            }
            eventsByDate[event.date].push(event);
        });
    }

    // 4. 渲染日期列表
    function renderDateList() {
        dateListContainer.innerHTML = '';
        Object.keys(eventsByDate).forEach(date => {
            const count = eventsByDate[date].length;
            const btn = document.createElement('button');
            btn.textContent = `${date} (${count})`;
            btn.className = (date === currentSelectedDate) ? 'active' : '';
            btn.onclick = () => selectDate(date);
            dateListContainer.appendChild(btn);
        });
    }

    // 5. 选择日期并渲染活动
    function selectDate(date) {
        currentSelectedDate = date;
        renderDateList();
        renderEventsForCurrentDate();
    }

    function renderEventsForCurrentDate() {
        const events = eventsByDate[currentSelectedDate] || [];
        const searchTerm = searchInput.value.trim().toLowerCase();
        const filteredEvents = searchTerm
            ? events.filter(event => event.city.toLowerCase().includes(searchTerm))
            : events;
        renderEventCards(filteredEvents);
    }

    // 6. 渲染活动卡片（保留原有搜索功能，搜索框放在顶部）
    function renderEventCards(events) {
        eventListContainer.innerHTML = '';
        if (events.length === 0) {
            const noEvents = document.createElement('div');
            noEvents.className = 'no-events';
            noEvents.innerHTML = "<p>No events found for your search.</p>";
            eventListContainer.appendChild(noEvents);
            return;
        }
        events.forEach(event => {
            const card = document.createElement('div');
            card.className = 'event-card';
            // Add data attribute for city identification (lowercase for hash matching)
            card.setAttribute('data-city', event.city.toLowerCase().replace(/\s+/g, ''));
            card.setAttribute('id', `event-${event.city.toLowerCase().replace(/\s+/g, '')}`);
            const timeDisplay = event.time === "Dusk" ? "🌇 Dusk" : `🕒 ${event.time}`;
            const rainDateHTML = event.rain_date ? `<p class="rain-date">🌧️ Rain Date: ${event.rain_date}</p>` : '';
            const specialNoteHTML = event.special_note ? `<p class="special-note">⭐ Note: ${event.special_note}</p>` : '';
            card.innerHTML = `
                <h2>${event.city}</h2>
                <p><strong>📍 Location:</strong> ${event.location}</p>
                <p><strong>🗓️ Date:</strong> ${event.date}</p>
                <p><strong>${timeDisplay}</strong></p>
                ${rainDateHTML}
                ${specialNoteHTML}
            `;
            eventListContainer.appendChild(card);
        });
    }

    // 7. 获取今天日期字符串（与数据格式一致）
    function getTodayDateStr() {
        const now = new Date();
        const options = { timeZone: 'America/New_York', year: 'numeric', month: '2-digit', day: '2-digit' };
        const parts = now.toLocaleDateString('en-CA', options).split('-');
        return `${parts[0]}-${parts[1]}-${parts[2]}`;
    }

    // 8. 生成结构化数据
    function generateStructuredData() {
        // Remove existing dynamic structured data
        const existingScripts = document.querySelectorAll('script[type="application/ld+json"][data-dynamic="true"]');
        existingScripts.forEach(script => script.remove());

        // Generate structured data for each event
        allEvents.forEach(event => {
            const structuredData = createEventStructuredData(event);
            const script = document.createElement('script');
            script.type = 'application/ld+json';
            script.setAttribute('data-dynamic', 'true');
            script.textContent = JSON.stringify(structuredData, null, 2);
            document.head.appendChild(script);
        });
    }

    // 9. 创建单个事件的结构化数据
    function createEventStructuredData(event) {
        // Calculate end time (assume 30 minutes duration for fireworks)
        const startDateTime = parseEventDateTime(event.date, event.time);
        const endDateTime = new Date(startDateTime.getTime() + 30 * 60 * 1000); // Add 30 minutes

        // Generate description
        const description = generateEventDescription(event);

        // Generate organizer name
        const organizerName = `City of ${event.city}`;

        return {
            "@context": "https://schema.org",
            "@type": "Event",
            "name": `${event.city} 4th of July Fireworks 2025`,
            "startDate": startDateTime.toISOString(),
            "endDate": endDateTime.toISOString(),
            "description": description,
            "image": "https://fireworksnearme.pages.dev/images/fireworks-generic.jpg",
            "location": {
                "@type": "Place",
                "name": event.location,
                "address": {
                    "@type": "PostalAddress",
                    "addressLocality": event.city,
                    "addressRegion": "MA",
                    "addressCountry": "US"
                }
            },
            "offers": {
                "@type": "Offer",
                "url": "https://fireworksnearme.pages.dev/",
                "price": "0",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
            },
            "organizer": {
                "@type": "Organization",
                "name": organizerName,
                "url": `https://www.${event.city.toLowerCase().replace(/\s+/g, '')}.gov/`
            },
            "eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
            "eventStatus": "https://schema.org/EventScheduled"
        };
    }

    // 10. 解析事件日期时间
    function parseEventDateTime(date, time) {
        const [year, month, day] = date.split('-');

        if (time === "Dusk") {
            // Assume dusk is around 8:00 PM in summer
            return new Date(`${year}-${month}-${day}T20:00:00-04:00`);
        } else {
            // Parse time in HH:MM format
            const [hours, minutes] = time.split(':');
            return new Date(`${year}-${month}-${day}T${hours}:${minutes}:00-04:00`);
        }
    }

    // 11. 生成事件描述
    function generateEventDescription(event) {
        let description = `Join us for the spectacular ${event.city} 4th of July fireworks display in 2025! `;
        description += `This exciting event will take place at ${event.location} on ${event.date}`;

        if (event.time === "Dusk") {
            description += ` at dusk.`;
        } else {
            description += ` at ${event.time}.`;
        }

        if (event.rain_date) {
            description += ` In case of rain, the event will be rescheduled to ${event.rain_date}.`;
        }

        if (event.special_note) {
            description += ` Special note: ${event.special_note}.`;
        }

        description += ` This free community event celebrates Independence Day with a beautiful fireworks show for the whole family to enjoy.`;

        return description;
    }

    // 12. Hash导航功能
    function handleHashNavigation() {
        const hash = window.location.hash.substring(1); // Remove the # symbol
        if (hash) {
            highlightCityEvent(hash);
        }
    }

    // 13. 根据城市名高亮显示事件
    function highlightCityEvent(cityName) {
        console.log('Highlighting city:', cityName);

        // Remove any existing highlights
        document.querySelectorAll('.event-card.highlighted').forEach(card => {
            card.classList.remove('highlighted');
        });

        // Find the event for this city
        const targetCity = cityName.toLowerCase().replace(/\s+/g, '');
        console.log('Looking for target city:', targetCity);

        const targetEvent = allEvents.find(event =>
            event.city.toLowerCase().replace(/\s+/g, '') === targetCity
        );

        console.log('Found target event:', targetEvent);

        if (targetEvent) {
            // Find the date for this event and select it first
            const eventDate = targetEvent.date;
            console.log('Event date:', eventDate);

            if (eventsByDate[eventDate]) {
                selectDate(eventDate);

                // Wait for the events to render, then highlight and scroll
                setTimeout(() => {
                    const eventCard = document.getElementById(`event-${targetCity}`);
                    console.log('Found event card:', eventCard);

                    if (eventCard) {
                        // Add highlight class
                        eventCard.classList.add('highlighted');
                        console.log('Added highlight class to:', eventCard);

                        // Scroll to the event with smooth animation
                        eventCard.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });

                        // Update page title to include city name
                        document.title = `${targetEvent.city} Fireworks 2025 - Fireworks Near Me`;
                    } else {
                        console.error('Event card not found for:', targetCity);
                    }
                }, 500); // Increased timeout to ensure rendering is complete
            } else {
                console.error('Event date not found in eventsByDate:', eventDate);
            }
        } else {
            console.error('Target event not found for city:', cityName);
        }
    }

    // 14. 监听hash变化
    window.addEventListener('hashchange', handleHashNavigation);
});